#!/bin/bash

# Script de test pour le système audio
# Ce script compile le projet et génère des fichiers de test si nécessaire

echo "=== Test du système audio ==="

# Vérifier que le dossier sound existe
if [ ! -d "sound" ]; then
    echo "Création du dossier sound/"
    mkdir sound
fi

# Générer des fichiers WAV de test si ils n'existent pas
if [ ! -f "sound/test_ambient.wav" ] && [ ! -f "sound/Zoo.mp3" ]; then
    echo "Génération de fichiers WAV de test..."
    
    # Compiler le générateur WAV
    if g++ -o wav_generator src/WavGenerator.cpp -std=c++17; then
        echo "Générateur WAV compilé avec succès"
        ./wav_generator
        rm wav_generator
    else
        echo "Erreur lors de la compilation du générateur WAV"
        echo "Vous devrez fournir manuellement un fichier audio dans sound/"
    fi
fi

# Vérifier les dépendances
echo ""
echo "=== Vérification des dépendances ==="

# Vérifier OpenAL
if pkg-config --exists openal; then
    echo "✓ OpenAL trouvé: $(pkg-config --modversion openal)"
else
    echo "✗ OpenAL non trouvé"
    echo "  Installez avec: pacman -S mingw-w64-x86_64-openal"
fi

# Vérifier GLFW
if pkg-config --exists glfw3; then
    echo "✓ GLFW trouvé: $(pkg-config --modversion glfw3)"
else
    echo "✗ GLFW non trouvé"
fi

# Vérifier GLEW
if pkg-config --exists glew; then
    echo "✓ GLEW trouvé: $(pkg-config --modversion glew)"
else
    echo "✗ GLEW non trouvé"
fi

echo ""
echo "=== Compilation du projet ==="

# Nettoyer et compiler
rm -rf build
mkdir build
cd build

if cmake -G "Unix Makefiles" ..; then
    echo "✓ Configuration CMake réussie"
    
    if make; then
        echo "✓ Compilation réussie"
        echo ""
        echo "=== Instructions d'utilisation ==="
        echo "1. Lancez le programme: ./ProjetOpenGL.exe"
        echo "2. Utilisez les contrôles audio:"
        echo "   - ESPACE: Jouer/Arrêter l'ambiance"
        echo "   - P: Pause/Reprendre"
        echo "   - O: Arrêter tous les sons"
        echo "3. Utilisez l'interface ImGui pour les réglages avancés"
        echo ""
        echo "Fichiers audio disponibles:"
        ls -la ../sound/ 2>/dev/null || echo "Aucun fichier audio trouvé"
    else
        echo "✗ Erreur de compilation"
        echo "Vérifiez que toutes les dépendances sont installées"
    fi
else
    echo "✗ Erreur de configuration CMake"
fi

cd ..

echo ""
echo "=== Fin du test ==="
