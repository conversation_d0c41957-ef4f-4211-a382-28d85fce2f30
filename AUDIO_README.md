# Système Audio 3D - Documentation

## Vue d'ensemble

Ce projet intègre un système audio 3D complet basé sur OpenAL, permettant de jouer des sons avec positionnement spatial, atténuation basée sur la distance, et effets Doppler.

## Architecture

### Classes principales

#### SoundManager
- **Rôle** : Gestionnaire principal du système audio
- **Responsabilités** :
  - Initialisation/fermeture d'OpenAL
  - Chargement et gestion des fichiers audio
  - Création des sources audio
  - Contrôle du volume principal
  - Mise à jour de la position de l'auditeur (caméra)

#### Sound
- **Rôle** : Représentation d'un fichier audio chargé
- **Responsabilités** :
  - Chargement de fichiers WAV et MP3
  - Stockage des données audio dans un buffer OpenAL
  - Métadonnées (durée, canaux, fréquence d'échantillonnage)

#### AudioSource
- **Rôle** : Source audio 3D dans l'espace
- **Responsabilités** :
  - Lecture de sons avec positionnement 3D
  - Contrôle du volume, pitch, et boucle
  - Atténuation basée sur la distance
  - Effet Doppler

## Utilisation

### Initialisation
```cpp
SoundManager soundManager;
if (!soundManager.Initialize()) {
    // Gestion d'erreur
}
```

### Chargement d'un son
```cpp
auto sound = soundManager.LoadSound("path/to/audio.wav", "sound_name");
```

### Création et utilisation d'une source audio
```cpp
auto audioSource = soundManager.CreateAudioSource();
audioSource->SetPosition(glm::vec3(10.0f, 0.0f, 5.0f));
audioSource->SetVolume(0.8f);
audioSource->Play(sound, true); // true = boucle
```

### Mise à jour (chaque frame)
```cpp
soundManager.Update();

// Synchroniser avec la caméra
float pos[3] = {camera.Position.x, camera.Position.y, camera.Position.z};
float forward[3] = {camera.Front.x, camera.Front.y, camera.Front.z};
float up[3] = {camera.Up.x, camera.Up.y, camera.Up.z};
soundManager.SetListenerPosition(pos, forward, up);
```

## Contrôles

### Clavier
- **ESPACE** : Jouer/Arrêter le son d'ambiance
- **P** : Pause/Reprendre la lecture
- **O** : Arrêter tous les sons

### Interface graphique (ImGui)
- Slider de volume principal
- Contrôles de lecture pour l'ambiance
- Réglage du pitch
- Boutons de contrôle global

## Formats supportés

### WAV (Support complet)
- PCM 8 bits et 16 bits
- Mono et stéréo
- Toutes fréquences d'échantillonnage

### MP3 (Support prévu)
- Nécessite l'implémentation complète de `dr_mp3.h`
- Actuellement, seule l'interface est présente

## Configuration audio 3D

### Paramètres de distance
```cpp
audioSource->SetReferenceDistance(1.0f);  // Distance de référence
audioSource->SetMaxDistance(100.0f);      // Distance maximale d'audibilité
audioSource->SetRolloffFactor(1.0f);      // Facteur d'atténuation
```

### Effet Doppler
```cpp
audioSource->SetVelocity(glm::vec3(5.0f, 0.0f, 0.0f)); // Vélocité en m/s
```

## Génération de fichiers de test

Un générateur de fichiers WAV est inclus pour les tests :

```bash
# Compiler le générateur
g++ -o wav_generator src/WavGenerator.cpp -std=c++17

# Générer des fichiers de test
./wav_generator
```

Cela créera :
- `sound/test_ambient.wav` (10 secondes d'ambiance)
- `sound/test_short.wav` (2 secondes pour tests rapides)

## Dépendances

### OpenAL
```bash
# MSYS2/MinGW64
pacman -S mingw-w64-x86_64-openal

# Ubuntu/Debian
sudo apt-get install libopenal-dev
```

### Bibliothèques header-only incluses
- `dr_mp3.h` (version simplifiée) - Pour le décodage MP3
- GLM - Pour les calculs vectoriels 3D

## Intégration dans le projet

Le système audio est intégré dans `main.cpp` avec :
1. Initialisation au démarrage
2. Mise à jour chaque frame
3. Synchronisation avec la caméra
4. Interface utilisateur ImGui
5. Nettoyage à la fermeture

## Extensibilité

Le système est conçu pour être facilement extensible :

### Ajouter de nouveaux formats audio
Implémentez de nouvelles méthodes dans la classe `Sound` :
```cpp
bool Sound::LoadOGG(const std::string& filePath);
```

### Ajouter des effets audio
Étendez la classe `AudioSource` avec de nouveaux paramètres :
```cpp
void AudioSource::SetReverb(float reverbLevel);
void AudioSource::SetLowPassFilter(float cutoffFreq);
```

### Streaming audio
Pour de gros fichiers, implémentez un système de streaming :
```cpp
class StreamingAudioSource : public AudioSource {
    // Implémentation du streaming avec buffers multiples
};
```

## Débogage

### Vérification des erreurs OpenAL
Toutes les classes incluent une méthode `CheckALError()` qui affiche les erreurs OpenAL avec des descriptions détaillées.

### Logs
Le système affiche des informations détaillées :
- Initialisation du système
- Chargement des fichiers
- États des sources audio
- Erreurs et avertissements

## Performance

### Optimisations incluses
- Nettoyage automatique des sources terminées
- Réutilisation des buffers audio
- Mise à jour conditionnelle des paramètres

### Recommandations
- Limitez le nombre de sources audio simultanées (généralement < 32)
- Utilisez des fichiers compressés (MP3/OGG) pour économiser la mémoire
- Préchargez les sons fréquemment utilisés
- Utilisez le streaming pour les musiques longues
