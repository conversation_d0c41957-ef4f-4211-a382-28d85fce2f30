#include "Sound.h"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <cstring>
#include <cstdint>

#ifndef HAVE_OPENAL
// Définitions factices pour compiler sans OpenAL
#define AL_NO_ERROR 0
#define AL_INVALID_NAME 1
#define AL_INVALID_ENUM 2
#define AL_INVALID_VALUE 3
#define AL_INVALID_OPERATION 4
#define AL_OUT_OF_MEMORY 5
#define AL_FORMAT_MONO8 1
#define AL_FORMAT_MONO16 2
#define AL_FORMAT_STEREO8 3
#define AL_FORMAT_STEREO16 4
typedef int ALenum;
typedef int ALsizei;
inline ALenum alGetError() { return AL_NO_ERROR; }
inline void alGenBuffers(int, unsigned int*) {}
inline void alDeleteBuffers(int, const unsigned int*) {}
inline void alBufferData(unsigned int, int, const void*, int, int) {}
#endif

// Pour le décodage MP3 (version simplifiée)
// Note: Dans une implémentation complète, utilisez dr_mp3.h avec DR_MP3_IMPLEMENTATION
// #define DR_MP3_IMPLEMENTATION
// #include "dr_mp3.h"

Sound::Sound()
    : m_bufferID(0)
    , m_duration(0.0f)
    , m_channels(0)
    , m_sampleRate(0)
    , m_bitsPerSample(0)
{
}

Sound::~Sound() {
    Unload();
}

bool Sound::LoadFromFile(const std::string& filePath) {
    // Extraire l'extension du fichier
    size_t dotPos = filePath.find_last_of('.');
    if (dotPos == std::string::npos) {
        std::cerr << "Sound: Impossible de déterminer le format du fichier '" << filePath << "'" << std::endl;
        return false;
    }

    std::string extension = filePath.substr(dotPos + 1);
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    m_fileName = filePath.substr(filePath.find_last_of("/\\") + 1);

    // Charger selon le format
    if (extension == "mp3") {
        return LoadMP3(filePath);
    } else if (extension == "wav") {
        return LoadWAV(filePath);
    } else {
        std::cerr << "Sound: Format de fichier non supporté '" << extension << "'" << std::endl;
        return false;
    }
}

bool Sound::LoadFromMemory(const void* data, size_t dataSize, int channels, int sampleRate, int bitsPerSample) {
    if (!data || dataSize == 0 || channels <= 0 || sampleRate <= 0 || (bitsPerSample != 8 && bitsPerSample != 16)) {
        std::cerr << "Sound: Paramètres invalides pour LoadFromMemory" << std::endl;
        return false;
    }

    // Libérer le buffer existant si nécessaire
    Unload();

#ifndef HAVE_OPENAL
    // Mode simulation sans OpenAL
    m_bufferID = 1; // ID factice
    m_channels = channels;
    m_sampleRate = sampleRate;
    m_bitsPerSample = bitsPerSample;
    m_duration = CalculateDuration(dataSize, channels, sampleRate, bitsPerSample);
    m_fileName = "Memory Buffer (Simulé)";
    std::cout << "Sound: Chargement simulé depuis la mémoire - " << m_channels << " canaux, "
              << m_sampleRate << " Hz, " << m_duration << "s" << std::endl;
    return true;
#endif

    // Générer un nouveau buffer OpenAL
    alGenBuffers(1, &m_bufferID);
    if (!CheckALError("Génération du buffer")) {
        return false;
    }

    // Déterminer le format OpenAL
    SoundFormat format = GetOpenALFormat(channels, bitsPerSample);
    if (format == 0) {
        std::cerr << "Sound: Format audio non supporté (" << channels << " canaux, " << bitsPerSample << " bits)" << std::endl;
        Unload();
        return false;
    }

    // Charger les données dans le buffer
    alBufferData(m_bufferID, format, data, static_cast<ALsizei>(dataSize), sampleRate);
    if (!CheckALError("Chargement des données dans le buffer")) {
        Unload();
        return false;
    }

    // Sauvegarder les propriétés
    m_channels = channels;
    m_sampleRate = sampleRate;
    m_bitsPerSample = bitsPerSample;
    m_duration = CalculateDuration(dataSize, channels, sampleRate, bitsPerSample);
    m_fileName = "Memory Buffer";

    std::cout << "Sound: Chargé depuis la mémoire - " << m_channels << " canaux, " 
              << m_sampleRate << " Hz, " << m_duration << "s" << std::endl;

    return true;
}

void Sound::Unload() {
    if (m_bufferID != 0) {
#ifdef HAVE_OPENAL
        alDeleteBuffers(1, &m_bufferID);
        CheckALError("Suppression du buffer");
#endif
        m_bufferID = 0;
    }

    m_duration = 0.0f;
    m_channels = 0;
    m_sampleRate = 0;
    m_bitsPerSample = 0;
    m_fileName.clear();
}

bool Sound::LoadMP3(const std::string& filePath) {
    std::cerr << "Sound: Le support MP3 nécessite l'implémentation complète de dr_mp3.h" << std::endl;
    std::cerr << "       Pour l'instant, utilisez des fichiers WAV" << std::endl;
    
    // TODO: Implémentation avec dr_mp3
    // Cette implémentation nécessiterait l'inclusion complète de dr_mp3.h
    // et l'utilisation de drmp3_open_file_and_read_pcm_frames_f32()
    
    return false;
}

bool Sound::LoadWAV(const std::string& filePath) {
    std::ifstream file(filePath, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Sound: Impossible d'ouvrir le fichier '" << filePath << "'" << std::endl;
        return false;
    }

    // Structure pour l'en-tête WAV
    struct WAVHeader {
        char riff[4];           // "RIFF"
        uint32_t fileSize;      // Taille du fichier - 8
        char wave[4];           // "WAVE"
        char fmt[4];            // "fmt "
        uint32_t fmtSize;       // Taille du chunk fmt
        uint16_t audioFormat;   // Format audio (1 = PCM)
        uint16_t channels;      // Nombre de canaux
        uint32_t sampleRate;    // Fréquence d'échantillonnage
        uint32_t byteRate;      // Bytes par seconde
        uint16_t blockAlign;    // Bytes par échantillon
        uint16_t bitsPerSample; // Bits par échantillon
        char data[4];           // "data"
        uint32_t dataSize;      // Taille des données audio
    };

    WAVHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(header));

    if (file.gcount() != sizeof(header)) {
        std::cerr << "Sound: Fichier WAV trop petit" << std::endl;
        return false;
    }

    // Vérifier la signature RIFF/WAVE
    if (std::memcmp(header.riff, "RIFF", 4) != 0 || std::memcmp(header.wave, "WAVE", 4) != 0) {
        std::cerr << "Sound: Fichier WAV invalide (signature RIFF/WAVE manquante)" << std::endl;
        return false;
    }

    // Vérifier le format PCM
    if (header.audioFormat != 1) {
        std::cerr << "Sound: Seul le format PCM est supporté (format trouvé: " << header.audioFormat << ")" << std::endl;
        return false;
    }

    // Vérifier le chunk data
    if (std::memcmp(header.data, "data", 4) != 0) {
        std::cerr << "Sound: Chunk 'data' non trouvé" << std::endl;
        return false;
    }

    // Lire les données audio
    std::vector<char> audioData(header.dataSize);
    file.read(audioData.data(), header.dataSize);

    if (file.gcount() != static_cast<std::streamsize>(header.dataSize)) {
        std::cerr << "Sound: Impossible de lire toutes les données audio" << std::endl;
        return false;
    }

    file.close();

    // Charger dans OpenAL
    bool success = LoadFromMemory(
        audioData.data(),
        header.dataSize,
        header.channels,
        header.sampleRate,
        header.bitsPerSample
    );

    if (success) {
        std::cout << "Sound: Fichier WAV '" << filePath << "' chargé avec succès" << std::endl;
    }

    return success;
}

SoundFormat Sound::GetOpenALFormat(int channels, int bitsPerSample) {
#ifdef HAVE_OPENAL
    if (channels == 1) {
        if (bitsPerSample == 8) return AL_FORMAT_MONO8;
        if (bitsPerSample == 16) return AL_FORMAT_MONO16;
    } else if (channels == 2) {
        if (bitsPerSample == 8) return AL_FORMAT_STEREO8;
        if (bitsPerSample == 16) return AL_FORMAT_STEREO16;
    }
    return 0; // Format non supporté
#else
    // Mode simulation
    return 1; // Format factice
#endif
}

float Sound::CalculateDuration(size_t dataSize, int channels, int sampleRate, int bitsPerSample) {
    if (channels <= 0 || sampleRate <= 0 || bitsPerSample <= 0) {
        return 0.0f;
    }

    size_t bytesPerSample = (bitsPerSample / 8) * channels;
    size_t totalSamples = dataSize / bytesPerSample;
    
    return static_cast<float>(totalSamples) / static_cast<float>(sampleRate);
}

bool Sound::CheckALError(const std::string& operation) {
    ALenum error = alGetError();
    if (error != AL_NO_ERROR) {
        std::cerr << "Sound: Erreur OpenAL lors de '" << operation << "': ";
        switch (error) {
            case AL_INVALID_NAME:
                std::cerr << "AL_INVALID_NAME";
                break;
            case AL_INVALID_ENUM:
                std::cerr << "AL_INVALID_ENUM";
                break;
            case AL_INVALID_VALUE:
                std::cerr << "AL_INVALID_VALUE";
                break;
            case AL_INVALID_OPERATION:
                std::cerr << "AL_INVALID_OPERATION";
                break;
            case AL_OUT_OF_MEMORY:
                std::cerr << "AL_OUT_OF_MEMORY";
                break;
            default:
                std::cerr << "Erreur inconnue (" << error << ")";
                break;
        }
        std::cerr << std::endl;
        return false;
    }
    return true;
}
