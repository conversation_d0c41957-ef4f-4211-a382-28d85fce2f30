#include <iostream>
#include <fstream>
#include <vector>
#include <cmath>
#include <cstdint>

/**
 * @brief Générateur de fichier WAV simple pour les tests
 * 
 * Ce fichier peut être compilé séparément pour générer un fichier WAV de test
 * si Zoo.mp3 n'est pas disponible ou si vous voulez tester le système audio.
 */

struct WAVHeader {
    char riff[4] = {'R', 'I', 'F', 'F'};
    uint32_t fileSize;
    char wave[4] = {'W', 'A', 'V', 'E'};
    char fmt[4] = {'f', 'm', 't', ' '};
    uint32_t fmtSize = 16;
    uint16_t audioFormat = 1; // PCM
    uint16_t channels;
    uint32_t sampleRate;
    uint32_t byteRate;
    uint16_t blockAlign;
    uint16_t bitsPerSample;
    char data[4] = {'d', 'a', 't', 'a'};
    uint32_t dataSize;
};

void generateTestWAV(const std::string& filename, float duration = 5.0f) {
    const int sampleRate = 44100;
    const int channels = 2; // Stéréo
    const int bitsPerSample = 16;
    const int bytesPerSample = bitsPerSample / 8;
    
    int totalSamples = static_cast<int>(duration * sampleRate);
    int dataSize = totalSamples * channels * bytesPerSample;
    
    WAVHeader header;
    header.channels = channels;
    header.sampleRate = sampleRate;
    header.bitsPerSample = bitsPerSample;
    header.byteRate = sampleRate * channels * bytesPerSample;
    header.blockAlign = channels * bytesPerSample;
    header.dataSize = dataSize;
    header.fileSize = sizeof(WAVHeader) - 8 + dataSize;
    
    std::vector<int16_t> audioData(totalSamples * channels);
    
    // Générer un son d'ambiance simple (mélange de fréquences)
    for (int i = 0; i < totalSamples; ++i) {
        float t = static_cast<float>(i) / sampleRate;
        
        // Mélange de plusieurs fréquences pour simuler une ambiance
        float sample = 0.0f;
        sample += 0.3f * std::sin(2.0f * M_PI * 220.0f * t); // La3
        sample += 0.2f * std::sin(2.0f * M_PI * 330.0f * t); // Mi4
        sample += 0.1f * std::sin(2.0f * M_PI * 440.0f * t); // La4
        sample += 0.05f * std::sin(2.0f * M_PI * 880.0f * t); // La5
        
        // Ajouter un peu de bruit pour l'ambiance
        sample += 0.02f * (static_cast<float>(rand()) / RAND_MAX - 0.5f);
        
        // Envelope pour éviter les clics
        float envelope = 1.0f;
        if (t < 0.1f) {
            envelope = t / 0.1f; // Fade in
        } else if (t > duration - 0.1f) {
            envelope = (duration - t) / 0.1f; // Fade out
        }
        
        sample *= envelope;
        
        // Convertir en 16-bit et dupliquer pour stéréo
        int16_t sampleValue = static_cast<int16_t>(sample * 32767.0f);
        audioData[i * 2] = sampleValue;     // Canal gauche
        audioData[i * 2 + 1] = sampleValue; // Canal droit
    }
    
    // Écrire le fichier
    std::ofstream file(filename, std::ios::binary);
    if (!file.is_open()) {
        std::cerr << "Impossible de créer le fichier " << filename << std::endl;
        return;
    }
    
    file.write(reinterpret_cast<const char*>(&header), sizeof(header));
    file.write(reinterpret_cast<const char*>(audioData.data()), dataSize);
    file.close();
    
    std::cout << "Fichier WAV généré: " << filename << std::endl;
    std::cout << "Durée: " << duration << "s, " << channels << " canaux, " 
              << sampleRate << " Hz, " << bitsPerSample << " bits" << std::endl;
}

int main() {
    std::cout << "Générateur de fichier WAV de test" << std::endl;
    
    // Générer un fichier d'ambiance de 10 secondes
    generateTestWAV("../sound/test_ambient.wav", 10.0f);
    
    // Générer un fichier plus court pour les tests
    generateTestWAV("../sound/test_short.wav", 2.0f);
    
    std::cout << "Fichiers générés dans le dossier sound/" << std::endl;
    std::cout << "Vous pouvez maintenant tester le système audio!" << std::endl;
    
    return 0;
}

/*
Pour compiler ce générateur séparément :
g++ -o wav_generator src/WavGenerator.cpp -std=c++17
./wav_generator

Ou ajoutez-le temporairement au CMakeLists.txt :
add_executable(WavGenerator src/WavGenerator.cpp)
*/
